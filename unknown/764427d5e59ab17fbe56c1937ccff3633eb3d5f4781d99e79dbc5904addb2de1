@if (isLoading()) {
    <div class="flex h-full items-center justify-center">
        <mat-spinner diameter="60"></mat-spinner>
    </div>
} @else {
    <div class="flex h-full">
        <div class="h-full w-1/4 border-r border-malou-color-background-dark bg-malou-color-background-light !pb-5 pt-[13px]">
            <ng-container [ngTemplateOutlet]="editBlockFormTemplate"></ng-container>
        </div>
        <div class="flex w-3/4 flex-col">
            <div class="flex w-full items-center border-b border-malou-color-background-dark px-5 py-3">
                <div class="flex w-1/2 justify-end gap-x-1">
                    <!-- TODO: V2 to do later [@hamza] -->
                    <!-- <button class="malou-btn-icon--secondary btn-sm" mat-icon-button>
                        <mat-icon color="primary" [svgIcon]="SvgIcon.DESKTOP_DISPLAY"></mat-icon>
                    </button>
                    <button class="malou-btn-icon--secondary btn-sm" mat-icon-button>
                        <mat-icon color="primary" [svgIcon]="SvgIcon.PHONE_DISPLAY"></mat-icon>
                    </button>-->
                </div>
                <div class="flex w-1/2 items-center justify-end gap-x-2">
                    <button
                        class="malou-btn-flat !text-[14px] font-extrabold !text-malou-color-text-2"
                        mat-button
                        [disabled]="isLoading()"
                        [ngClass]="{ 'opacity-50': shouldDisableModal() }"
                        (click)="closeEditModal()">
                        {{ 'store_locator.edit_modal.cancel' | translate }}
                    </button>
                    <app-button
                        [text]="'store_locator.edit_modal.draft' | translate"
                        [loading]="shouldDisableModal()"
                        [disabled]="isBlockInError() || !shouldAllowToSaveAsDraftOrPublish()"
                        [theme]="'secondary'"
                        (buttonClick)="onSaveAsDraft()">
                    </app-button>
                    <app-button
                        [text]="'store_locator.edit_modal.publish' | translate"
                        [loading]="shouldDisableModal()"
                        [disabled]="isBlockInError() || !shouldAllowToSaveAsDraftOrPublish()"
                        [theme]="'primary'"
                        (buttonClick)="onPublish()">
                    </app-button>
                </div>
            </div>
            <div
                class="flex h-[100vh] origin-top-left flex-col overflow-scroll px-3"
                [ngStyle]="{ fontFamily: textFontFamilyClass() }"
                [ngClass]="{ 'pointer-events-none opacity-50': shouldDisableModal() }"
                #sitePreview>
                <app-store-locator-edit-page-header-block></app-store-locator-edit-page-header-block>
                <app-store-locator-edit-centralization-page-map-block></app-store-locator-edit-centralization-page-map-block>
                <app-store-locator-edit-page-footer-block></app-store-locator-edit-page-footer-block>
            </div>
        </div>
    </div>
}

<ng-template #editBlockFormTemplate>
    @switch (selectedBlock()) {
        @case (StoreLocatorCentralizationBlockType.MAP) {
            <app-store-locator-edit-centralization-page-map-block-form></app-store-locator-edit-centralization-page-map-block-form>
        }
    }
</ng-template>
