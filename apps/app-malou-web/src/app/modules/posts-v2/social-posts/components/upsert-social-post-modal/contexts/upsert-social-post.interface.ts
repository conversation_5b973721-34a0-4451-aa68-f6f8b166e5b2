import { I<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TiktokPrivacyStatus } from '@malou-io/package-utils';

import { IUpsertSocialPost } from ':modules/posts-v2/social-posts/models/upsert-social-post';
import { Platform } from ':shared/models';

export interface UpsertSocialPostState {
    post: IUpsertSocialPost;
    isAutoSaving: boolean;
    isSubmitting: boolean;
    isLoadingPost: boolean;
    isLoadingLocation: boolean;
    duplicateToPlatforms: PlatformKey[];
    connectedSocialPlatforms: Platform[];
    userTagsHistory: { username: string; count: number; igAccount: IGAccount }[];
    tiktokCreatorInfo: {
        username: string;
        privacyStatusValues: TiktokPrivacyStatus[];
        isCommentDisabled: boolean;
        isDuetDisabled: boolean;
        isStitchDisabled: boolean;
    };
    instagramCollaboratorsHistory: { username: string; count: number; igAccount: IGAccount }[];
}
